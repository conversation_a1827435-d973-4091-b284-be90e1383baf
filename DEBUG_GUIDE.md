# Guia de Debugging - DigAI Negocia

Este guia explica como usar diferentes técnicas de debugging nesta aplicação Next.js com TypeScript.

## 🛠️ Configuração Inicial

### 1. Arquivos de Configuração Criados

- `.vscode/launch.json` - Configurações de debug para VS Code
- `.vscode/settings.json` - Configurações do editor
- `src/utils/debugHelpers.ts` - Utilitários de debugging
- `src/components/DebugExample.tsx` - Exemplo prático

### 2. Modificações no tsconfig.json

Adicionado suporte a source maps para melhor debugging:
```json
"sourceMap": true,
"inlineSourceMap": false
```

## 🔍 Métodos de Debugging

### 1. **Debugger no Browser (Mais Simples)**

#### Como usar:
1. Adicione `debugger;` no seu código onde quer pausar
2. <PERSON><PERSON> o DevTools do browser (F12)
3. Execute a função que contém o debugger
4. O código pausará na linha do debugger

#### Exemplo:
```typescript
export async function minhaFuncao() {
  const data = await fetchData();
  debugger; // Pausa aqui
  return processData(data);
}
```

### 2. **VS Code Debugger**

#### Configurações disponíveis:
- **Next.js: debug server-side** - Para debugar código do servidor
- **Next.js: debug client-side** - Para debugar código do cliente
- **Next.js: debug full stack** - Para debugar ambos

#### Como usar:
1. Coloque breakpoints clicando na margem esquerda do editor
2. Vá para a aba "Run and Debug" (Ctrl+Shift+D)
3. Selecione a configuração desejada
4. Clique em "Start Debugging" (F5)

### 3. **Console Logging Avançado**

#### Usando os utilitários criados:
```typescript
import { debugLog, inspectObject, conditionalDebugger } from '@/utils/debugHelpers';

// Logs categorizados
debugLog.info('Informação geral', data);
debugLog.error('Erro encontrado', error);
debugLog.warn('Aviso importante', warning);
debugLog.api('GET /api/users', 'GET', requestData);

// Inspeção detalhada de objetos
inspectObject(complexObject, 'Objeto Complexo');

// Debugger condicional
conditionalDebugger(user.id === null, 'Usuário sem ID');
```

### 4. **Medição de Performance**

```typescript
import { measurePerformance } from '@/utils/debugHelpers';

// Wrapper para medir tempo de execução
const myOptimizedFunction = measurePerformance(
  async (param1, param2) => {
    // sua lógica aqui
    return result;
  },
  'myOptimizedFunction'
);
```

## 🎯 Debugging por Contexto

### **APIs e Services**

O `metricsService.ts` já foi configurado com debugging:
- Logs de início e fim de operações
- Inspeção de respostas da API
- Debugger condicional para casos de erro
- Medição de performance

### **Componentes React**

Use o `useDebugRender` para monitorar re-renders:
```typescript
import { useDebugRender } from '@/utils/debugHelpers';

const MeuComponente = ({ prop1, prop2 }) => {
  useDebugRender('MeuComponente', { prop1, prop2 });
  
  // resto do componente
};
```

### **Hooks Personalizados**

```typescript
const useMyHook = (dependency) => {
  useEffect(() => {
    debugLog.info('Hook executado', { dependency });
    
    if (process.env.NODE_ENV === 'development') {
      console.log('🪝 Hook state changed');
      // debugger; // descomente para pausar
    }
  }, [dependency]);
};
```

## 🚀 Comandos Úteis

### Iniciar aplicação com debug:
```bash
# Desenvolvimento normal
npm run dev

# Com debug do Node.js
NODE_OPTIONS='--inspect' npm run dev

# Com debug detalhado
DEBUG=* npm run dev
```

### Debugging no Terminal:
```bash
# Ver logs detalhados do Next.js
NEXT_DEBUG=1 npm run dev

# Debug específico de módulos
DEBUG=next:* npm run dev
```

## 🔧 Dicas Avançadas

### 1. **Debugging de Network Requests**

No DevTools:
- Aba Network para ver todas as requisições
- Aba Console para logs personalizados
- Aba Sources para debugger e breakpoints

### 2. **React DevTools**

Instale a extensão React DevTools para:
- Inspecionar componentes
- Ver props e state
- Profiling de performance

### 3. **Debugging de Estado**

```typescript
// Para Redux/Zustand
const useStore = create((set, get) => ({
  // ... state
  debug: () => {
    if (process.env.NODE_ENV === 'development') {
      console.log('🏪 Store state:', get());
      debugger;
    }
  }
}));
```

### 4. **Debugging de Erros**

```typescript
// Error Boundary personalizado
class DebugErrorBoundary extends React.Component {
  componentDidCatch(error, errorInfo) {
    if (process.env.NODE_ENV === 'development') {
      debugLog.error('Error Boundary ativado', { error, errorInfo });
      debugger; // Pausa quando há erro
    }
  }
}
```

## 📱 Debugging Mobile/Responsivo

### Chrome DevTools:
1. F12 → Toggle device toolbar
2. Selecione dispositivo
3. Use "Remote debugging" para dispositivos reais

### Safari (iOS):
1. Ative "Web Inspector" no iOS
2. Safari → Develop → [Dispositivo] → [Página]

## ⚡ Debugging de Performance

### 1. **React Profiler**
```typescript
import { Profiler } from 'react';

<Profiler id="MyComponent" onRender={(id, phase, actualDuration) => {
  debugLog.info('Render performance', { id, phase, actualDuration });
}}>
  <MyComponent />
</Profiler>
```

### 2. **Web Vitals**
```typescript
// pages/_app.tsx
export function reportWebVitals(metric) {
  if (process.env.NODE_ENV === 'development') {
    debugLog.info('Web Vital', metric);
  }
}
```

## 🎨 Debugging Visual

### CSS Debugging:
```css
/* Adicione temporariamente para ver layouts */
* { outline: 1px solid red !important; }
.debug-grid { background: repeating-linear-gradient(45deg, transparent, transparent 10px, rgba(255,0,0,0.1) 10px, rgba(255,0,0,0.1) 20px); }
```

## 📋 Checklist de Debugging

- [ ] Breakpoints colocados nos pontos críticos
- [ ] Console.log removido antes do commit
- [ ] Source maps habilitados
- [ ] DevTools aberto durante desenvolvimento
- [ ] Logs categorizados (info, warn, error)
- [ ] Performance medida em funções críticas
- [ ] Error boundaries implementados
- [ ] Debugging condicional para casos específicos

## 🚨 Importante

- **Sempre remova** `debugger;` e `console.log` antes de fazer commit
- **Use** os utilitários de debug que só funcionam em desenvolvimento
- **Teste** em diferentes browsers e dispositivos
- **Monitore** performance em produção com ferramentas adequadas

---

**Exemplo Prático**: Veja `src/components/DebugExample.tsx` para um exemplo completo de como implementar debugging em um componente React.
