'use client';

import React, { useState, useEffect } from 'react';
import { debugLog, useDebugRender, conditionalDebugger, inspectObject } from '@/utils/debugHelpers';
import { fetchFirstMessagesSentMetrics } from '@/services/metricsService';

interface DebugExampleProps {
  initialDate?: string;
}

export const DebugExample: React.FC<DebugExampleProps> = ({ initialDate }) => {
  // Debug de renders do componente
  useDebugRender('DebugExample', { initialDate });

  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleFetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      debugLog.info('Iniciando fetch de dados', { initialDate });
      
      // Debugger condicional - só pausa se não tiver data inicial
      conditionalDebugger(!initialDate, 'Data inicial não fornecida');

      const startDate = initialDate || '2024-01-01';
      const endDate = new Date().toISOString().split('T')[0];
      
      const result = await fetchFirstMessagesSentMetrics(startDate, endDate);
      
      // Inspecionar resultado
      inspectObject(result, 'Resultado da API');
      
      setData(result);
      debugLog.info('Dados carregados com sucesso', result);
      
    } catch (err) {
      debugLog.error('Erro ao carregar dados', err);
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    debugLog.info('useEffect executado', { initialDate });
    
    // Exemplo de debugger no useEffect
    if (process.env.NODE_ENV === 'development') {
      console.log('🔄 useEffect - DebugExample montado');
      // Descomente para pausar na montagem do componente
      // debugger;
    }
  }, [initialDate]);

  return (
    <div className="p-4 border rounded-lg">
      <h2 className="text-xl font-bold mb-4">Exemplo de Debugging</h2>
      
      <div className="space-y-4">
        <button
          onClick={handleFetchData}
          disabled={loading}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
        >
          {loading ? 'Carregando...' : 'Buscar Dados'}
        </button>

        {error && (
          <div className="p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            Erro: {error}
          </div>
        )}

        {data && (
          <div className="p-3 bg-green-100 border border-green-400 text-green-700 rounded">
            <h3 className="font-semibold">Dados carregados:</h3>
            <pre className="mt-2 text-sm overflow-auto">
              {JSON.stringify(data, null, 2)}
            </pre>
          </div>
        )}
      </div>

      {/* Informações de debug visíveis apenas em desenvolvimento */}
      {process.env.NODE_ENV === 'development' && (
        <div className="mt-4 p-3 bg-yellow-100 border border-yellow-400 text-yellow-700 rounded">
          <h4 className="font-semibold">🔍 Debug Info:</h4>
          <ul className="mt-2 text-sm">
            <li>Initial Date: {initialDate || 'Não fornecida'}</li>
            <li>Loading: {loading.toString()}</li>
            <li>Has Data: {!!data}</li>
            <li>Has Error: {!!error}</li>
          </ul>
        </div>
      )}
    </div>
  );
};
