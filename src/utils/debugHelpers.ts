// Utilitários para debugging na aplicação

/**
 * Logger personalizado para debugging
 */
export const debugLog = {
  info: (message: string, data?: any) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`🔍 [DEBUG] ${message}`, data || '');
    }
  },
  
  error: (message: string, error?: any) => {
    if (process.env.NODE_ENV === 'development') {
      console.error(`❌ [ERROR] ${message}`, error || '');
    }
  },
  
  warn: (message: string, data?: any) => {
    if (process.env.NODE_ENV === 'development') {
      console.warn(`⚠️ [WARN] ${message}`, data || '');
    }
  },
  
  api: (endpoint: string, method: string, data?: any) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`🌐 [API] ${method} ${endpoint}`, data || '');
    }
  }
};

/**
 * Função para pausar execução com debugger condicional
 */
export const conditionalDebugger = (condition: boolean, message?: string) => {
  if (condition && process.env.NODE_ENV === 'development') {
    console.log(`🛑 Debugger pausado: ${message || 'Condição atendida'}`);
    debugger;
  }
};

/**
 * Wrapper para medir performance de funções
 */
export const measurePerformance = <T extends (...args: any[]) => any>(
  fn: T,
  label: string
): T => {
  return ((...args: any[]) => {
    if (process.env.NODE_ENV === 'development') {
      console.time(`⏱️ ${label}`);
      const result = fn(...args);
      
      if (result instanceof Promise) {
        return result.finally(() => {
          console.timeEnd(`⏱️ ${label}`);
        });
      } else {
        console.timeEnd(`⏱️ ${label}`);
        return result;
      }
    }
    return fn(...args);
  }) as T;
};

/**
 * Função para inspecionar objetos complexos
 */
export const inspectObject = (obj: any, label: string = 'Object') => {
  if (process.env.NODE_ENV === 'development') {
    console.group(`🔍 Inspecionando ${label}`);
    console.log('Tipo:', typeof obj);
    console.log('Valor:', obj);
    console.log('JSON:', JSON.stringify(obj, null, 2));
    console.groupEnd();
  }
};

/**
 * Hook para debugging de re-renders em React (usar em components)
 */
export const useDebugRender = (componentName: string, props?: any) => {
  if (process.env.NODE_ENV === 'development') {
    console.log(`🔄 [RENDER] ${componentName}`, props || '');
  }
};
