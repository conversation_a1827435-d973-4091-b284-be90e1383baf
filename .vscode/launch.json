{"version": "0.2.0", "configurations": [{"name": "Next.js: debug server-side", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/next/dist/bin/next", "args": ["dev", "-p", "3390"], "cwd": "${workspaceFolder}", "console": "integratedTerminal", "skipFiles": ["<node_internals>/**"], "env": {"NODE_OPTIONS": "--inspect"}}, {"name": "Next.js: debug client-side", "type": "chrome", "request": "launch", "url": "http://localhost:3390", "webRoot": "${workspaceFolder}", "sourceMapPathOverrides": {"webpack://_N_E/*": "${webRoot}/*", "webpack:///./*": "${webRoot}/*", "webpack:///./~/*": "${webRoot}/node_modules/*", "webpack://?:*/*": "${webRoot}/*"}}, {"name": "Next.js: debug full stack", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/next/dist/bin/next", "args": ["dev", "-p", "3390"], "cwd": "${workspaceFolder}", "console": "integratedTerminal", "skipFiles": ["<node_internals>/**"], "serverReadyAction": {"pattern": "- Local:.+(https?://.+)", "uriFormat": "%s", "action": "debugWithChrome"}, "env": {"NODE_OPTIONS": "--inspect"}}]}